#include <WiFi.h>
#ifdef ESP32
  #include <WebServer.h>
#else
  #include <ESP8266WebServer.h>
  #define WebServer ESP8266WebServer
#endif

const char* ssid = "A2001";
const char* password = "support@2020";

WebServer server(80);

// Simple session management
bool isAuthenticated = false;
unsigned long sessionStartTime = 0;
const unsigned long SESSION_TIMEOUT = 3600000; // 1 hour in milliseconds

// Authentication credentials
const String ADMIN_USERNAME = "admin";
const String ADMIN_PASSWORD = "admin";

// Login page HTML
const char login_html[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
  <title>Guard Tour System - Login</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .login-container {
      background: white;
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 400px;
    }
    .logo-container {
      text-align: center;
      margin-bottom: 2rem;
    }
    .logo {
      width: 80px;
      height: 80px;
      background: #667eea;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 1rem;
    }
    .system-title {
      color: #333;
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    .system-subtitle {
      color: #666;
      font-size: 0.9rem;
    }
    .form-group {
      margin-bottom: 1.5rem;
    }
    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      color: #333;
      font-weight: 500;
    }
    .form-group input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e1e5e9;
      border-radius: 5px;
      font-size: 1rem;
      transition: border-color 0.3s;
    }
    .form-group input:focus {
      outline: none;
      border-color: #667eea;
    }
    .login-btn {
      width: 100%;
      padding: 0.75rem;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.3s;
    }
    .login-btn:hover {
      background: #5a6fd8;
    }
    .error-message {
      color: #e74c3c;
      text-align: center;
      margin-top: 1rem;
      display: none;
    }
    .demo-credentials {
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 5px;
      margin-top: 1rem;
      font-size: 0.85rem;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="logo-container">
      <div class="logo">GT</div>
      <div class="system-title">Guard Tour System</div>
      <div class="system-subtitle">Secure Access Portal</div>
    </div>

    <form id="loginForm">
      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" name="username" required>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required>
      </div>

      <button type="submit" class="login-btn">Login</button>

      <div id="errorMessage" class="error-message">
        Invalid username or password. Please try again.
      </div>

      <div class="demo-credentials">
        <strong>Demo Credentials:</strong><br>
        Username: admin<br>
        Password: admin
      </div>
    </form>
  </div>

  <script>
    document.getElementById('loginForm').addEventListener('submit', function(e) {
      e.preventDefault();

      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;

      // Send login request
      fetch('/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
      })
      .then(response => response.text())
      .then(data => {
        if (data === 'SUCCESS') {
          window.location.href = '/dashboard';
        } else {
          document.getElementById('errorMessage').style.display = 'block';
        }
      })
      .catch(error => {
        document.getElementById('errorMessage').style.display = 'block';
      });
    });
  </script>
</body>
</html>
)rawliteral";

// Main dashboard HTML with left sidebar
const char dashboard_html[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
  <title>Guard Tour System - Dashboard</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      height: 100vh;
      overflow: hidden;
    }
    .container {
      display: flex;
      height: 100vh;
    }
    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
      color: white;
      overflow-y: auto;
      box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }
    .sidebar-header {
      padding: 1.5rem;
      border-bottom: 1px solid #34495e;
      text-align: center;
    }
    .logo {
      width: 60px;
      height: 60px;
      background: #3498db;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 1rem;
    }
    .system-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
    }
    .system-subtitle {
      font-size: 0.8rem;
      opacity: 0.8;
    }
    .menu-section {
      padding: 1rem 0;
    }
    .menu-item {
      margin-bottom: 0.5rem;
    }
    .menu-header {
      padding: 0.75rem 1.5rem;
      background: rgba(255,255,255,0.1);
      cursor: pointer;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: background 0.3s;
    }
    .menu-header:hover {
      background: rgba(255,255,255,0.15);
    }
    .menu-header.active {
      background: rgba(255,255,255,0.2);
    }
    .menu-content {
      display: none;
      background: rgba(0,0,0,0.1);
    }
    .menu-content.active {
      display: block;
    }
    .sub-option {
      display: block;
      padding: 0.75rem 2rem;
      color: rgba(255,255,255,0.9);
      text-decoration: none;
      transition: all 0.3s;
      border-left: 3px solid transparent;
    }
    .sub-option:hover {
      background: rgba(255,255,255,0.1);
      border-left-color: #3498db;
      color: white;
    }
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    .top-bar {
      background: white;
      padding: 1rem 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .page-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #2c3e50;
    }
    .logout-btn {
      padding: 0.5rem 1rem;
      background: #e74c3c;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: background 0.3s;
    }
    .logout-btn:hover {
      background: #c0392b;
    }
    .content-area {
      flex: 1;
      padding: 2rem;
      overflow-y: auto;
    }
    .welcome-card {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 2rem;
    }
    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-top: 2rem;
    }
    .status-card {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }
    .status-value {
      font-size: 2rem;
      font-weight: bold;
      color: #3498db;
      margin-bottom: 0.5rem;
    }
    .status-label {
      color: #666;
      font-size: 0.9rem;
    }
    .result-area {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-top: 1rem;
      min-height: 100px;
    }
    .chevron {
      transition: transform 0.3s;
    }
    .chevron.rotated {
      transform: rotate(90deg);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">GT</div>
        <div class="system-title">Guard Tour</div>
        <div class="system-subtitle">Control Panel</div>
      </div>

      <div class="menu-section">
        <!-- System Configuration -->
        <div class="menu-item">
          <div class="menu-header" onclick="toggleMenu(this)">
            <span>System Configuration</span>
            <span class="chevron">▶</span>
          </div>
          <div class="menu-content">
            <a href="#" class="sub-option" data-action="config1">Network Settings</a>
            <a href="#" class="sub-option" data-action="config2">Device Preferences</a>
            <a href="#" class="sub-option" data-action="config3">Security Options</a>
          </div>
        </div>

        <!-- Data Management -->
        <div class="menu-item">
          <div class="menu-header" onclick="toggleMenu(this)">
            <span>Data Management</span>
            <span class="chevron">▶</span>
          </div>
          <div class="menu-content">
            <a href="#" class="sub-option" data-action="data1">View Logs</a>
            <a href="#" class="sub-option" data-action="data2">Export Reports</a>
            <a href="#" class="sub-option" data-action="data3">Storage Status</a>
          </div>
        </div>

        <!-- Device Control -->
        <div class="menu-item">
          <div class="menu-header" onclick="toggleMenu(this)">
            <span>Device Control</span>
            <span class="chevron">▶</span>
          </div>
          <div class="menu-content">
            <a href="#" class="sub-option" data-action="control1">Restart Device</a>
            <a href="#" class="sub-option" data-action="control2">Update Firmware</a>
            <a href="#" class="sub-option" data-action="control3">Factory Reset</a>
          </div>
        </div>

        <!-- Monitoring -->
        <div class="menu-item">
          <div class="menu-header" onclick="toggleMenu(this)">
            <span>Monitoring</span>
            <span class="chevron">▶</span>
          </div>
          <div class="menu-content">
            <a href="#" class="sub-option" data-action="monitor1">Real-time Stats</a>
            <a href="#" class="sub-option" data-action="monitor2">Performance Metrics</a>
            <a href="#" class="sub-option" data-action="monitor3">System Health</a>
          </div>
        </div>
      </div>
    </div>

    <div class="main-content">
      <div class="top-bar">
        <div class="page-title">Dashboard</div>
        <button class="logout-btn" onclick="logout()">Logout</button>
      </div>

      <div class="content-area">
        <div class="welcome-card">
          <h2>Welcome to Guard Tour System</h2>
          <p>Monitor and control your security patrol system from this central dashboard.</p>

          <div class="status-grid">
            <div class="status-card">
              <div class="status-value">Online</div>
              <div class="status-label">System Status</div>
            </div>
            <div class="status-card">
              <div class="status-value" id="uptime">00:00:00</div>
              <div class="status-label">Uptime</div>
            </div>
            <div class="status-card">
              <div class="status-value">4</div>
              <div class="status-label">Active Modules</div>
            </div>
            <div class="status-card">
              <div class="status-value">Connected</div>
              <div class="status-label">Network Status</div>
            </div>
          </div>
        </div>

        <div class="result-area">
          <h3>System Messages</h3>
          <div id="result">Select an option from the menu to get started.</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let startTime = Date.now();

    // Update uptime counter
    function updateUptime() {
      const now = Date.now();
      const diff = now - startTime;
      const hours = Math.floor(diff / 3600000);
      const minutes = Math.floor((diff % 3600000) / 60000);
      const seconds = Math.floor((diff % 60000) / 1000);

      document.getElementById('uptime').textContent =
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    setInterval(updateUptime, 1000);

    // Toggle menu functionality
    function toggleMenu(header) {
      const content = header.nextElementSibling;
      const chevron = header.querySelector('.chevron');

      // Close other menus
      document.querySelectorAll('.menu-header').forEach(h => {
        if (h !== header) {
          h.classList.remove('active');
          h.nextElementSibling.classList.remove('active');
          h.querySelector('.chevron').classList.remove('rotated');
        }
      });

      // Toggle current menu
      header.classList.toggle('active');
      content.classList.toggle('active');
      chevron.classList.toggle('rotated');
    }

    // Handle sub-option clicks
    document.querySelectorAll('.sub-option').forEach(option => {
      option.addEventListener('click', function(e) {
        e.preventDefault();
        const action = this.getAttribute('data-action');
        document.getElementById('result').innerHTML = `Processing ${action}...`;

        // Send action to ESP32
        fetch(`/action?type=${action}`)
          .then(response => response.text())
          .then(data => {
            document.getElementById('result').innerHTML = data;
          })
          .catch(error => {
            document.getElementById('result').innerHTML = 'Error: ' + error;
          });
      });
    });

    // Logout function
    function logout() {
      fetch('/logout')
        .then(() => {
          window.location.href = '/';
        });
    }
  </script>
</body>
</html>
)rawliteral";

void setup() {
  Serial.begin(115200);

  // Connect to Wi-Fi
  WiFi.mode(WIFI_STA);

  Serial.println("Initializing WiFi...");
  WiFi.begin(ssid, password);

  unsigned long startAttemptTime = millis();
  while (WiFi.status() != WL_CONNECTED && millis() - startAttemptTime < 30000) {
    delay(500);
    Serial.print(".");
  }

  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("\nFailed to connect to WiFi");
    // Restart function - platform specific
    #ifdef ESP32
      ESP.restart();
    #elif defined(ESP8266)
      ESP.restart();
    #else
      // For other Arduino platforms, just continue or implement reset
      Serial.println("Please reset the device manually");
      while(1); // Halt execution
    #endif
  }

  Serial.println("\nConnected to WiFi");
  Serial.print("IP Address: ");
  Serial.println(WiFi.localIP());

  // Helper function to check session validity
  auto checkSession = []() -> bool {
    if (!isAuthenticated) return false;
    if (millis() - sessionStartTime > SESSION_TIMEOUT) {
      isAuthenticated = false;
      return false;
    }
    return true;
  };

  // Route for root / web page (login page)
  server.on("/", [](){
    server.send_P(200, "text/html", login_html);
  });

  // Login handler
  server.on("/login", HTTP_POST, [](){
    String username = server.arg("username");
    String password = server.arg("password");

    if (username == ADMIN_USERNAME && password == ADMIN_PASSWORD) {
      isAuthenticated = true;
      sessionStartTime = millis();
      server.send(200, "text/plain", "SUCCESS");
    } else {
      server.send(200, "text/plain", "FAILED");
    }
  });

  // Dashboard route (protected)
  server.on("/dashboard", [](){
    if (checkSession()) {
      server.send_P(200, "text/html", dashboard_html);
    } else {
      server.sendHeader("Location", "/");
      server.send(302, "text/plain", "");
    }
  });

  // Logout handler
  server.on("/logout", [](){
    isAuthenticated = false;
    sessionStartTime = 0;
    server.send(200, "text/plain", "OK");
  });

  // Action handler (protected)
  server.on("/action", [](){
    if (!checkSession()) {
      server.send(401, "text/plain", "Unauthorized");
      return;
    }

    String action = server.arg("type");
    String response;

    // Process action without transferring sensitive data
    if(action.startsWith("config")) {
      response = "System configuration updated successfully";
      // (Add actual configuration handling here)
    }
    else if(action.startsWith("data")) {
      response = "Data processing completed (storage not implemented)";
      // Removed SPIFFS dependency - can be replaced with EEPROM or other storage
    }
    else if(action.startsWith("control")) {
      response = "Device command executed successfully";
      // (Add actual control handling here)
    }
    else if(action.startsWith("monitor")) {
      response = "Monitoring data processed successfully";
      // (Add actual monitoring handling here)
    }
    else {
      response = "Unknown action requested";
    }

    server.send(200, "text/plain", response);
  });

  // Start server
  server.begin();
}

void loop() {
  // Handle client requests
  server.handleClient();

  // Background tasks can be added here
  delay(10); // Small delay to prevent watchdog issues
}
