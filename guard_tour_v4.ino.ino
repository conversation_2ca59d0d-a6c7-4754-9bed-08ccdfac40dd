#include <WiFi.h>
#ifdef ESP32
  #include <WebServer.h>
#else
  #include <ESP8266WebServer.h>
  #define WebServer ESP8266WebServer
#endif

const char* ssid = "A2001";
const char* password = "support@2020";

WebServer server(80);

// HTML content with accordion structure
const char index_html[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
  <title>ESP32-S3 Control Panel</title>
  <style>
    .accordion {
      width: 100%;
      max-width: 600px;
      margin: 0 auto;
      font-family: Arial, sans-serif;
    }
    .accordion-item {
      border: 1px solid #ddd;
      margin-bottom: 10px;
      border-radius: 4px;
      overflow: hidden;
    }
    .accordion-header {
      background-color: #f0f0f0;
      padding: 15px;
      cursor: pointer;
      font-weight: bold;
      border-bottom: 1px solid #ddd;
    }
    .accordion-content {
      display: none;
      padding: 15px;
      background-color: #fff;
    }
    .accordion-content.active {
      display: block;
    }
    .sub-option {
      display: block;
      padding: 10px;
      margin: 5px 0;
      background-color: #e9e9e9;
      border-radius: 3px;
      cursor: pointer;
      text-decoration: none;
      color: #333;
    }
    .sub-option:hover {
      background-color: #d9d9d9;
    }
  </style>
</head>
<body>
  <h1 style="text-align: center;">ESP32-S3 Control Panel</h1>
  <div class="accordion">
    <!-- Accordion Item 1 -->
    <div class="accordion-item">
      <div class="accordion-header">System Configuration</div>
      <div class="accordion-content">
        <a href="#" class="sub-option" data-action="config1">Network Settings</a>
        <a href="#" class="sub-option" data-action="config2">Device Preferences</a>
        <a href="#" class="sub-option" data-action="config3">Security Options</a>
      </div>
    </div>
    
    <!-- Accordion Item 2 -->
    <div class="accordion-item">
      <div class="accordion-header">Data Management</div>
      <div class="accordion-content">
        <a href="#" class="sub-option" data-action="data1">View Logs</a>
        <a href="#" class="sub-option" data-action="data2">Export Reports</a>
        <a href="#" class="sub-option" data-action="data3">Storage Status</a>
      </div>
    </div>
    
    <!-- Accordion Item 3 -->
    <div class="accordion-item">
      <div class="accordion-header">Device Control</div>
      <div class="accordion-content">
        <a href="#" class="sub-option" data-action="control1">Restart Device</a>
        <a href="#" class="sub-option" data-action="control2">Update Firmware</a>
        <a href="#" class="sub-option" data-action="control3">Factory Reset</a>
      </div>
    </div>
    
    <!-- Accordion Item 4 -->
    <div class="accordion-item">
      <div class="accordion-header">Monitoring</div>
      <div class="accordion-content">
        <a href="#" class="sub-option" data-action="monitor1">Real-time Stats</a>
        <a href="#" class="sub-option" data-action="monitor2">Performance Metrics</a>
        <a href="#" class="sub-option" data-action="monitor3">System Health</a>
      </div>
    </div>
  </div>
  
  <div id="result" style="margin-top: 20px; text-align: center;"></div>
  
  <script>
    // Accordion functionality
    document.querySelectorAll('.accordion-header').forEach(header => {
      header.addEventListener('click', () => {
        const content = header.nextElementSibling;
        content.classList.toggle('active');
      });
    });
    
    // Handle sub-option clicks
    document.querySelectorAll('.sub-option').forEach(option => {
      option.addEventListener('click', function(e) {
        e.preventDefault();
        const action = this.getAttribute('data-action');
        document.getElementById('result').innerHTML = `Processing ${action}...`;
        
        // Send action to ESP32
        fetch(`/action?type=${action}`)
          .then(response => response.text())
          .then(data => {
            document.getElementById('result').innerHTML = data;
          })
          .catch(error => {
            document.getElementById('result').innerHTML = 'Error: ' + error;
          });
      });
    });
  </script>
</body>
</html>
)rawliteral";

void setup() {
  Serial.begin(115200);

  // Connect to Wi-Fi
  WiFi.mode(WIFI_STA);

  Serial.println("Initializing WiFi...");
  WiFi.begin(ssid, password);

  unsigned long startAttemptTime = millis();
  while (WiFi.status() != WL_CONNECTED && millis() - startAttemptTime < 30000) {
    delay(500);
    Serial.print(".");
  }

  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("\nFailed to connect to WiFi");
    // Restart function - platform specific
    #ifdef ESP32
      ESP.restart();
    #elif defined(ESP8266)
      ESP.restart();
    #else
      // For other Arduino platforms, just continue or implement reset
      Serial.println("Please reset the device manually");
      while(1); // Halt execution
    #endif
  }

  Serial.println("\nConnected to WiFi");
  Serial.print("IP Address: ");
  Serial.println(WiFi.localIP());

  // Route for root / web page
  server.on("/", [](){
    server.send_P(200, "text/html", index_html);
  });

  // Action handler (processes requests without transferring data)
  server.on("/action", [](){
    String action = server.arg("type");
    String response;

    // Process action without transferring sensitive data
    if(action.startsWith("config")) {
      response = "System configuration updated";
      // (Add actual configuration handling here)
    }
    else if(action.startsWith("data")) {
      response = "Data processed on device";
      // Removed SPIFFS dependency - can be replaced with EEPROM or other storage
      response = "Data processing completed (storage not implemented)";
    }
    else if(action.startsWith("control")) {
      response = "Device command executed";
      // (Add actual control handling here)
    }
    else if(action.startsWith("monitor")) {
      response = "Monitoring data processed";
      // (Add actual monitoring handling here)
    }
    else {
      response = "Unknown action";
    }

    server.send(200, "text/plain", response);
  });

  // Start server
  server.begin();
}

void loop() {
  // Handle client requests
  server.handleClient();

  // Background tasks can be added here
  delay(10); // Small delay to prevent watchdog issues
}
